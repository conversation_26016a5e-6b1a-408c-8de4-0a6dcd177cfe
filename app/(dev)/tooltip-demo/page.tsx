"use client"

import React, { useState } from 'react'
import MonitorCard, { MonitorCardData } from '@/components/shared/MonitorCard'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import Sidebar from '@/components/shared/Sidebar'
import Breadcrumb from '@/components/shared/Breadcrumb'
import {
  Activity,
  BarChart3,
  TrendingUp,
  Zap,
  Network,
  Database,
  Info,
  MousePointer,
  Eye
} from 'lucide-react'

export default function TooltipDemoPage() {
  const [activeNavItem, setActiveNavItem] = useState("TooltipDemo")

  // Demo monitors showcasing different chart types with tooltips
  // Using design token system instead of hardcoded colors
  const demoMonitors: MonitorCardData[] = [
    {
      id: "demo-1",
      name: "Network Traffic Monitor",
      status: "active",
      type: "network",
      showMetrics: true,
      chartType: "area",
      iconType: "network",
      statusColor: "green",
      dataPattern: "normal",
      description: "Area chart with gradient fill"
    },
    {
      id: "demo-2",
      name: "Transaction Volume",
      status: "active",
      type: "transaction",
      showMetrics: true,
      chartType: "line",
      iconType: "activity",
      statusColor: "green",
      dataPattern: "spike",
      description: "Line chart with spike pattern"
    },
    {
      id: "demo-3",
      name: "Resource Usage",
      status: "warning",
      type: "network",
      showMetrics: true,
      chartType: "stacked-bar",
      iconType: "bar-chart-3",
      statusColor: "orange",
      dataPattern: "step",
      description: "Stacked bar chart with step data"
    },
    {
      id: "demo-4",
      name: "Performance Metrics",
      status: "active",
      type: "transaction",
      showMetrics: true,
      chartType: "multi-line",
      iconType: "trending-up",
      statusColor: "green",
      dataPattern: "oscillating",
      description: "Multi-line chart with oscillating pattern"
    },
    {
      id: "demo-5",
      name: "Correlation Analysis",
      status: "active",
      type: "network",
      showMetrics: true,
      chartType: "bubble",
      iconType: "zap",
      statusColor: "green",
      dataPattern: "random-walk",
      description: "Bubble chart for correlation analysis"
    },
    {
      id: "demo-6",
      name: "Gradient Flow",
      status: "active",
      type: "network",
      showMetrics: true,
      chartType: "gradient-area",
      iconType: "database",
      statusColor: "green",
      dataPattern: "sawtooth",
      description: "Advanced gradient area chart"
    }
  ]

  return (
    <div className="flex h-screen bg-background">
      {/* Left Navigation Sidebar */}
      <Sidebar
        activeNavItem={activeNavItem}
        onNavItemChange={setActiveNavItem}
      />

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col ml-12 overflow-hidden">
        {/* Top Navigation Bar with Breadcrumb */}
        <Breadcrumb items={[
          { label: "Home", href: "/" },
          { label: "Development Tools", href: "#" },
          { label: "Tooltip Demo", isActive: true }
        ]} />

        {/* Page Content */}
        <div className="flex-1 p-6 overflow-y-auto">
          <div className="max-w-7xl mx-auto">
            {/* Header */}
            <div className="mb-8">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-2 bg-primary/10 rounded-lg">
                  <Activity className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold text-foreground">
                    Monitor Card Tooltip Demo
                  </h1>
                  <p className="text-muted-foreground">
                    Interactive tooltip system for chart data visualization
                  </p>
                </div>
                <Badge variant="secondary" className="ml-auto">
                  Development Tool
                </Badge>
              </div>
              <p className="text-muted-foreground text-lg">
                Hover over the charts below to see interactive tooltips with metric legends and formatted values
              </p>
            </div>

            {/* Instructions Card */}
            <Card className="mb-8 border-primary/20 bg-primary/5">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-primary">
                  <MousePointer className="h-5 w-5" />
                  How to Test Tooltips
                </CardTitle>
                <CardDescription>
                  Follow these steps to explore the tooltip functionality
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <div className="flex items-start gap-3">
                      <div className="w-6 h-6 bg-primary/20 rounded-full flex items-center justify-center text-xs font-medium text-primary mt-0.5">1</div>
                      <div>
                        <p className="font-medium">Hover over charts</p>
                        <p className="text-sm text-muted-foreground">Move your mouse over any chart area to activate tooltips</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <div className="w-6 h-6 bg-primary/20 rounded-full flex items-center justify-center text-xs font-medium text-primary mt-0.5">2</div>
                      <div>
                        <p className="font-medium">Explore data points</p>
                        <p className="text-sm text-muted-foreground">Move along the chart to see different time periods and values</p>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-3">
                    <div className="flex items-start gap-3">
                      <div className="w-6 h-6 bg-primary/20 rounded-full flex items-center justify-center text-xs font-medium text-primary mt-0.5">3</div>
                      <div>
                        <p className="font-medium">Observe formatting</p>
                        <p className="text-sm text-muted-foreground">Notice color-coded legends and properly formatted units</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-3">
                      <div className="w-6 h-6 bg-primary/20 rounded-full flex items-center justify-center text-xs font-medium text-primary mt-0.5">4</div>
                      <div>
                        <p className="font-medium">Compare chart types</p>
                        <p className="text-sm text-muted-foreground">Try different chart types to see various tooltip behaviors</p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Demo Grid */}
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-foreground mb-4 flex items-center gap-2">
                <Eye className="h-5 w-5 text-primary" />
                Interactive Chart Examples
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {demoMonitors.map((monitor) => (
                  <div key={monitor.id} className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div className="text-sm font-medium text-foreground">
                        {monitor.chartType?.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())} Chart
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {monitor.type}
                      </Badge>
                    </div>
                    <MonitorCard
                      monitor={monitor}
                      onClick={(m) => console.log('Tooltip demo clicked:', m.name)}
                    />
                    <p className="text-xs text-muted-foreground">
                      {monitor.description}
                    </p>
                  </div>
                ))}
              </div>
            </div>

            {/* Feature Documentation */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
              {/* Tooltip Features */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Info className="h-5 w-5 text-primary" />
                    Tooltip Features
                  </CardTitle>
                  <CardDescription>
                    Advanced tooltip system capabilities
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-semibold mb-2 text-primary">Visual Elements</h4>
                      <ul className="space-y-1 text-sm text-muted-foreground">
                        <li>• Color-coded metric indicators matching chart colors</li>
                        <li>• Semi-transparent background with subtle border</li>
                        <li>• Consistent typography following design system</li>
                        <li>• Smooth animations and responsive positioning</li>
                        <li>• Theme-aware styling (light/dark mode)</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-semibold mb-2 text-primary">Data Display</h4>
                      <ul className="space-y-1 text-sm text-muted-foreground">
                        <li>• Human-readable metric names and descriptions</li>
                        <li>• Appropriate units (Mbps, ms, %, /min, etc.)</li>
                        <li>• Formatted numeric values with proper precision</li>
                        <li>• Time labels with consistent formatting</li>
                        <li>• Health indicator context when available</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Technical Implementation */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart3 className="h-5 w-5 text-primary" />
                    Technical Implementation
                  </CardTitle>
                  <CardDescription>
                    How tooltips are implemented in the system
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-semibold mb-2 text-primary">Chart Integration</h4>
                      <ul className="space-y-1 text-sm text-muted-foreground">
                        <li>• Built on Recharts tooltip system</li>
                        <li>• Custom tooltip component with design system styling</li>
                        <li>• Automatic metric detection and formatting</li>
                        <li>• Chart type-specific tooltip behaviors</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-semibold mb-2 text-primary">Performance</h4>
                      <ul className="space-y-1 text-sm text-muted-foreground">
                        <li>• Optimized rendering with React memoization</li>
                        <li>• Efficient data formatting utilities</li>
                        <li>• Minimal DOM manipulation for smooth interactions</li>
                        <li>• Responsive design with container queries</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Usage Guidelines */}
            <Card>
              <CardHeader>
                <CardTitle>Usage Guidelines</CardTitle>
                <CardDescription>
                  Best practices for implementing tooltips in monitor cards
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <h4 className="font-semibold mb-2 text-green-600">✅ Do</h4>
                    <ul className="space-y-1 text-sm text-muted-foreground">
                      <li>• Use consistent color coding</li>
                      <li>• Include appropriate units</li>
                      <li>• Format numbers for readability</li>
                      <li>• Provide meaningful metric names</li>
                      <li>• Test across different chart types</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2 text-red-600">❌ Don't</h4>
                    <ul className="space-y-1 text-sm text-muted-foreground">
                      <li>• Overcrowd with too much information</li>
                      <li>• Use inconsistent formatting</li>
                      <li>• Ignore accessibility requirements</li>
                      <li>• Hardcode colors or styles</li>
                      <li>• Forget mobile responsiveness</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2 text-blue-600">💡 Tips</h4>
                    <ul className="space-y-1 text-sm text-muted-foreground">
                      <li>• Test with real data ranges</li>
                      <li>• Consider tooltip positioning</li>
                      <li>• Validate color contrast</li>
                      <li>• Use semantic HTML structure</li>
                      <li>• Document custom implementations</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
